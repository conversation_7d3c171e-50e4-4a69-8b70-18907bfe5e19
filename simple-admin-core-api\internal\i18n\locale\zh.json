{"common": {"success": "成功", "failed": "失败", "updateSuccess": "更新成功", "updateFailed": "更新失败", "createSuccess": "新建成功", "createFailed": "新建失败", "deleteSuccess": "删除成功", "deleteFailed": "删除失败", "changeStatusSuccess": "状态修改成功", "changeStatusFailed": "状态修改失败", "targetNotExist": "目标不存在", "databaseError": "数据库错误", "redisError": "Redis 错误", "permissionDeny": "用户无权限访问此接口", "constraintError": "操作失败: 数据冲突", "validationError": "操作失败: 校验失败", "notSingularError": "操作失败: 数据不唯一", "serviceUnavailable": "服务不可用，请检查服务是否已启用", "cacheError": "缓存出错"}, "apiGroup": {"cloud_file_tag": "云文件标签管理", "cloud_file": "云文件管理", "storage_provider": "云存储服务管理", "member_rank": "等级管理", "member": "会员管理", "file_tag": "文件标签管理", "file": "文件管理", "task_log": "任务日志", "task": "任务管理", "position": "职位管理", "department": "部门管理", "token": "令牌管理", "oauth": "认证管理", "dictionary": "字典管理", "api": "接口管理", "authority": "授权管理", "captcha": "验证码", "menu": "菜单管理", "role": "角色管理", "user": "用户管理", "email_provider": "电子邮件服务配置", "email_log": "电子邮件发送日志", "sms_provider": "短信服务配置", "sms_log": "短信发送日志", "message_sender": "消息发送", "configuration": "参数配置", "banner": "轮播图管理", "segment_article_record": "栏目文章记录管理", "segment": "栏目管理", "cms": "社区管理", "websitecms": "官网管理", "groupManage": "圈子管理", "tagManage": "话题管理", "article": "文章管理", "reply": "回复管理", "segmentArticleRecord": "栏目文章记录管理", "mtms": "售后管理", "maintenInfo": "维修单管理", "product": "产品管理", "address": "地址管理", "province": "省份管理", "city": "城市管理", "area": "区管理", "street": "街道管理", "userAddress": "用户地址管理", "appVersion": "App版本管理", "devHistory": "发展历程管理", "cmsTagManage": "标签管理", "appApi": "APP接口管理", "appRole": "APP角色管理", "group": "圈子管理", "groupFollow": "圈子关注管理", "groupMember": "圈子成员管理", "maintenOrder": "维修单管理", "sync": "同步管理", "tag": "话题管理", "tagRecord": "话题记录管理", "userAddres": "用户地址管理", "appapi": "appapi", "approle": "approle", "mallOrder": "订单管理", "mallOrderItem": "订单项管理", "mallOrderLog": "订单日志管理", "mallProduct": "商品管理", "mallProductCategory": "分类管理", "mallProductQuota": "配额管理", "mallProductSku": "SKU管理", "mallSubscription": "订阅管理", "mallUsageRecord": "使用记录管理", "mallRefund": "退款管理", "mallDelivery": "物流管理", "mallPayment": "支付管理", "mallPaymentLog": "支付日志管理", "mallRefundExpress": "退款物流管理", "mallLicense": "许可证管理", "mallLicenseDevice": "许可证设备管理", "mallSupplier": "供应商管理", "mallSupplierCommission": "供应商分成管理", "mallApplication": "应用管理", "boloLexicon": "播咯秘词库管理", "boluomiBanner": "播咯秘轮播图管理", "app": "应用管理", "analytics": "数据分析", "analyticsApplication": "分析应用管理", "analyticsCohort": "分析分群管理", "analyticsDailySummary": "每日数据汇总", "analyticsDevice": "设备分析管理", "analyticsEvent": "事件分析管理", "analyticsEventDefinition": "事件定义管理", "analyticsRealTimeMetric": "实时指标分析", "analyticsUser": "用户分析管理", "rentalDevice": "租赁设备管理", "rentalDeviceMessage": "租赁设备消息管理"}, "apiDesc": {"createConfiguration": "创建参数配置", "updateConfiguration": "更新参数配置", "deleteConfiguration": "删除参数配置", "getConfigurationList": "获取参数配置列表", "getConfigurationById": "通过ID获取参数配置", "createCloudFileTag": "创建云文件标签", "updateCloudFileTag": "更新云文件标签", "deleteCloudFileTag": "删除云文件标签", "getCloudFileTagList": "获取云文件标签列表", "getCloudFileTagById": "通过ID获取云文件标签", "createFileTag": "创建文件标签", "updateFileTag": "更新文件标签", "deleteFileTag": "删除文件标签", "getFileTagList": "获取文件标签列表", "getFileTagById": "通过ID获取文件标签", "createStorageProvider": "创建云储存提供商", "updateStorageProvider": "更新云储存提供商", "deleteStorageProvider": "删除云储存提供商", "getStorageProviderList": "获取云储存提供商列表", "getStorageProviderById": "通过ID获取云储存提供商", "uploadFileToCloud": "上传文件到云服务", "createCloudFile": "创建云文件", "updateCloudFile": "更新云文件", "deleteCloudFile": "删除云文件", "getCloudFileList": "获取云文件列表", "getCloudFileById": "通过ID获取云文件", "sendEmail": "发送电子邮件", "sendSms": "发送短信", "createEmailLog": "创建邮件发送日志", "updateEmailLog": "更新邮件发送日志", "deleteEmailLog": "删除邮件发送日志", "getEmailLogList": "获取邮件发送日志列表", "getEmailLogById": "通过ID获取邮件发送日志", "createSmsLog": "创建短信发送日志", "updateSmsLog": "更新短信发送日志", "deleteSmsLog": "删除短信发送日志", "getSmsLogList": "获取短信发送日志列表", "getSmsLogById": "通过ID获取短信发送日志", "createSmsProvider": "创建短信服务配置", "updateSmsProvider": "更新短信服务配置", "deleteSmsProvider": "删除短信服务配置", "getSmsProviderList": "获取短信服务配置列表", "getSmsProviderById": "通过ID获取短信服务配置", "createEmailProvider": "创建电子邮件服务配置", "updateEmailProvider": "更新电子邮件服务配置", "deleteEmailProvider": "删除电子邮件服务配置", "getEmailProviderList": "获取电子邮件服务配置列表", "getEmailProviderById": "通过ID获取电子邮件服务配置", "userLogin": "用户登录（必须）", "userRegister": "用户注册（必须）", "userChangePassword": "用户修改密码", "userInfo": "获取用户信息（必须）", "userList": "获取用户列表", "userModify": "修改用户信息", "userPermissions": "获取用户授权码（必须）", "userProfile": "获取用户个人信息（必须）", "updateProfile": "修改用户个人信息（必须）", "createUser": "新增用户", "updateUser": "修改用户", "deleteUser": "删除用户", "batchDeleteUser": "批量删除用户", "logout": "退出登陆（必须）", "updateUserStatus": "更新用户状态", "refreshToken": "获取 refresh token", "accessToken": "获取 access token", "createRole": "新建角色信息", "updateRole": "更新角色信息", "deleteRole": "删除角色信息", "roleList": "获取角色列表", "setRoleStatus": "设置角色状态", "createMenu": "新建菜单", "updateMenu": "更新菜单", "menuInfo": "获取菜单信息", "menuList": "获取菜单列表", "menuRoleList": "获取角色菜单列表(必须)", "deleteMenu": "删除菜单", "roleMenu": "获取角色菜单（必须）", "createMenuParam": "创建菜单参数", "updateMenuParam": "更新菜单参数", "deleteMenuParam": "删除菜单额外参数", "menuParamListByMenuId": "获取某个菜单的额外参数列表", "createApi": "创建API信息", "updateApi": "修改API信息", "deleteAPI": "删除API", "APIInfo": "获取API信息", "APIList": "获取API列表", "authorityApiList": "获取授权列表中的API", "createOrUpdateApiAuthority": "创建或修改API授权", "APIAuthorityOfRole": "获取角色的API授权信息", "createOrUpdateMenuAuthority": "创建或修改菜单授权", "menuAuthorityOfRole": "获取角色的菜单授权信息", "captcha": "获取验证码（必须）", "uploadFile": "上传文件", "fileList": "文件列表", "deleteFile": "删除文件", "updateFileInfo": "更新文件信息", "setPublicStatus": "改变文件公开状态", "downloadFile": "下载文件", "createDictionary": "创建字典信息", "updateDictionary": "更新字典信息", "deleteDictionary": "删除字典信息", "getDictionaryList": "获取字典列表", "getDictionaryById": "通过ID获取字典信息", "getDictionaryDetailById": "通过ID获取字典数据信息", "createDictionaryDetail": "创建字典键值信息", "updateDictionaryDetail": "更新字典键值信息", "deleteDictionaryDetail": "删除字典键值信息", "getDictionaryListDetail": "获取字典键值列表", "getDictionaryDetailByDictionaryName": "通过字典名称查询键值", "createProvider": "创建登录提供商", "updateProvider": "更新登录提供商", "deleteProvider": "删除登录提供商", "getProviderList": "获取提供商列表", "oauthLogin": "第三方登录", "createToken": "创建Token", "updateToken": "更新Token", "deleteToken": "删除Token", "getTokenList": "获取Token列表", "batchDeleteToken": "批量删除Token", "forceLoggingOut": "强制登出", "createDepartment": "创建部门信息", "updateDepartment": "更新部门信息", "deleteDepartment": "删除一个部门", "batchDeleteDepartment": "批量删除部门", "getDepartmentList": "获取部门列表", "updateDepartmentStatus": "修改部门状态", "createPosition": "创建职位信息", "updatePosition": "更新职位信息", "deletePosition": "删除一个职位", "batchDeletePosition": "批量删除职位", "getPositionList": "获取职位列表", "updatePositionStatus": "修改职位状态", "createMember": "创建会员信息", "updateMember": "更新会员信息", "deleteMember": "删除一个会员", "batchDeleteMember": "批量删除会员", "getMemberList": "获取会员列表", "updateMemberStatus": "修改会员状态", "createMemberRank": "创建会员等级信息", "updateMemberRank": "更新会员等级信息", "deleteMemberRank": "删除一个会员等级", "batchDeleteMemberRank": "批量删除会员等级", "getMemberRankList": "获取会员等级列表", "getUserById": "通过ID获取用户信息", "getRoleById": "通过ID获取角色信息", "getMenuById": "通过ID获取菜单信息", "getApiById": "通过ID获取API信息", "getMenuParamById": "通过ID获取菜单参数信息", "getMemberById": "通过ID获取注册会员信息", "getMemberRankById": "通过ID获取会员等级信息", "getDepartmentById": "通过ID获取部门信息", "getPositionById": "通过ID获取职位信息", "getProviderById": "通过ID获取第三方信息", "getTokenById": "通过ID获取Token信息", "createTask": "创建任务", "updateTask": "更新任务", "deleteTask": "删除一个任务", "getTaskList": "获取任务列表", "getTaskById": "通过ID获取任务信息", "createTaskLog": "创建任务日志", "updateTaskLog": "更新任务日志", "deleteTaskLog": "删除任务日志", "getTaskLogList": "获取任务日志列表", "getTaskLogById": "通过ID获取任务日志", "createArticle": "创建文章信息", "updateArticle": "更新文章信息", "deleteArticle": "删除文章信息", "getArticleList": "获取文章信息列表", "getArticleById": "通过ID获取文章信息", "createSegment": "创建栏目", "updateSegment": "更新栏目", "deleteSegment": "删除栏目", "getSegmentList": "获取栏目列表", "getSegmentById": "通过ID获取栏目", "createBanner": "创建栏目", "updateBanner": "更新栏目", "deleteBanner": "删除栏目", "getBannerList": "获取栏目列表", "getBannerById": "通过ID获取栏目", "createUserAdres": "创建用户地址", "updateUserAdres": "更新用户地址", "deleteUserAdres": "删除用户地址", "getUserAdresList": "获取用户地址列表", "getUserAdresById": "通过ID获取用户地址", "createReply": "创建回复", "updateReply": "更新回复", "deleteReply": "删除回复", "getReplyList": "获取回复列表", "getReplyById": "通过ID获取回复", "createApp": "创建#App", "updateApp": "更新#App", "deleteApp": "删除#App", "getAppList": "获取#App列表", "getAppById": "通过ID获取#App", "createGroup": "创建圈子信息", "updateGroup": "更新圈子信息", "deleteGroup": "删除圈子信息", "getGroupList": "获取圈子信息列表", "getGroupById": "通过ID获取圈子信息", "createTag": "创建话题信息", "updateTag": "更新话题信息", "deleteTag": "删除话题信息", "getTagList": "获取话题信息列表", "getTagById": "通过ID获取话题信息", "getTagByGroupId": "通过圈子ID获取话题信息", "createProduct": "创建产品信息", "updateProduct": "更新产品信息", "deleteProduct": "删除产品信息", "getProductList": "获取产品信息列表", "getProductById": "通过ID获取产品信息", "createMaintenInfo": "创建维修单信息", "updateMaintenInfo": "更新维修单信息", "deleteMaintenInfo": "删除维修单信息", "getMaintenInfoList": "获取维修单信息列表", "getMaintenInfoById": "通过ID获取维修单信息", "createProvince": "创建省份信息", "updateProvince": "更新省份信息", "deleteProvince": "删除省份信息", "getProvinceList": "获取省份信息列表", "getProvinceById": "通过ID获取省份信息", "createCity": "创建城市信息", "updateCity": "更新城市信息", "deleteCity": "删除城市信息", "getCityList": "获取城市信息列表", "getCityById": "通过ID获取城市信息", "createArea": "创建区信息", "updateArea": "更新区信息", "deleteArea": "删除区信息", "getAreaList": "获取区信息列表", "getAreaById": "通过ID获取区信息", "createStreet": "创建街道信息", "updateStreet": "更新街道信息", "deleteStreet": "删除街道信息", "getStreetList": "获取街道信息列表", "getStreetById": "通过ID获取街道信息", "createAppversion": "创建APP版本信息", "updateAppversion": "更新APP版本信息", "deleteAppversion": "删除APP版本信息", "getAppversionList": "获取APP版本信息列表", "getAppversionById": "通过ID获取APP版本信息", "createSegmentArticleRecord": "创建栏目文章", "updateSegmentArticleRecord": "更新栏目文章", "deleteSegmentArticleRecord": "删除栏目文章", "getSegmentArticleRecordList": "获取栏目文章列表", "getSegmentArticleRecordById": "通过ID获取栏目文章", "getArticleListWithoutDraft": "获取非草稿文章列表", "createHistory": "创建发展历程", "updateHistory": "更新发展历程", "deleteHistory": "删除发展历程", "getHistoryList": "获取发展历程列表", "getHistoryById": "通过ID获取发展历程", "createAppApi": "创建App接口", "updateAppApi": "更新App接口", "deleteAppApi": "删除App接口", "getAppApiList": "获取App接口列表", "getAppApiById": "通过ID获取App接口", "createAppRole": "创建App角色", "updateAppRole": "更新App角色", "deleteAppRole": "删除App角色", "getAppRoleList": "获取App角色列表", "getAppRoleById": "通过ID获取App角色", "getAppVersionById": "通过ID获取App版本", "createAppVersion": "创建App版本", "deleteAppVersion": "删除App版本", "getAppVersionList": "获取App版本列表", "updateAppVersion": "更新App版本", "getAppapiById": "通过ID获取App接口", "createAppapi": "创建App接口", "deleteAppapi": "删除App接口", "getAppapiList": "获取App接口列表", "updateAppapi": "更新App接口", "getApproleById": "通过ID获取App角色", "createApprole": "创建App角色", "deleteApprole": "删除App角色", "getApproleList": "获取App角色列表", "updateApprole": "更新App角色", "auditArticle": "审核文章", "cancelTopArticle": "取消置顶文章", "topArticle": "置顶文章", "getGroupFollowById": "通过ID获取圈子关注", "createGroupFollow": "创建圈子关注", "deleteGroupFollow": "删除圈子关注", "getGroupFollowList": "获取圈子关注列表", "updateGroupFollow": "更新圈子关注", "getGroupMemberById": "通过ID获取圈子成员", "createGroupMember": "创建圈子成员", "deleteGroupMember": "删除圈子成员", "getGroupMemberList": "获取圈子成员列表", "updateGroupMember": "更新圈子成员", "getMaintenOrderById": "通过ID获取维修单", "createMaintenOrder": "创建维修单", "deleteMaintenOrder": "删除维修单", "getMaintenOrderList": "获取维修单列表", "updateMaintenOrder": "更新维修单", "syncArticleCommentNum": "同步文章回复数", "syncArticleLikeNum": "同步文章点赞数", "syncArticleViewNum": "同步文章浏览数", "syncFollowAndFansInfo": "同步关注、粉丝信息", "syncFollowInfo": "同步关注、粉丝信息", "syncGroupInfo": "同步群组信息", "syncTagInfo": "同步话题信息", "syncUserInfo": "同步用户信息", "syncArticleData": "同步文章数据", "getTagListByGroupId": "通过圈子ID获取话题信息", "getTagRecordById": "通过ID获取话题记录", "createTagRecord": "创建话题记录", "deleteTagRecord": "删除话题记录", "getTagRecordList": "获取话题记录列表", "updateTagRecord": "更新话题记录", "getUserList": "获取用户列表", "updateUserForbiddenTime": "更新用户封禁时间", "updateUserMutedTime": "更新用户禁言时间", "getUserAddresById": "通过ID获取用户地址", "createUserAddres": "创建用户地址", "deleteUserAddres": "删除用户地址", "getUserAddresList": "获取用户地址列表", "updateUserAddres": "更新用户地址", "getApiAuthority": "获取角色Api权限列表", "getMallOrderById": "通过ID获取商城订单信息", "createMallOrder": "创建商城订单信息", "deleteMallOrder": "删除商城订单信息", "getMallOrderList": "获取商城订单信息列表", "updateMallOrder": "更新商城订单信息", "getMallOrderItemById": "通过ID获取商城订单项信息", "createMallOrderItem": "创建商城订单项信息", "deleteMallOrderItem": "删除商城订单项信息", "getMallOrderItemList": "获取商城订单项信息列表", "updateMallOrderItem": "更新商城订单项信息", "getMallOrderLogById": "通过ID获取商城订单日志信息", "createMallOrderLog": "创建商城订单日志信息", "deleteMallOrderLog": "删除商城订单日志信息", "getMallOrderLogList": "获取商城订单日志信息列表", "updateMallOrderLog": "更新商城订单日志信息", "getMallProductById": "通过ID获取商城商品信息", "createMallProduct": "创建商城商品信息", "deleteMallProduct": "删除商城商品信息", "getMallProductList": "获取商城商品信息列表", "updateMallProduct": "更新商城商品信息", "getMallProductCategoryById": "通过ID获取商城商品分类信息", "createMallProductCategory": "创建商城商品分类信息", "deleteMallProductCategory": "删除商城商品分类信息", "getMallProductCategoryList": "获取商城商品分类信息列表", "updateMallProductCategory": "更新商城商品分类信息", "getMallProductQuotaById": "通过ID获取商城商品配额信息", "createMallProductQuota": "创建商城商品配额信息", "deleteMallProductQuota": "删除商城商品配额信息", "getMallProductQuotaList": "获取商城商品配额信息列表", "updateMallProductQuota": "更新商城商品配额信息", "getMallProductSkuById": "通过ID获取商城商品SKU信息", "createMallProductSku": "创建商城商品SKU信息", "deleteMallProductSku": "删除商城商品SKU信息", "getMallProductSkuList": "获取商城商品SKU信息列表", "updateMallProductSku": "更新商城商品SKU信息", "getMallSubscriptionById": "通过ID获取商城订阅信息", "createMallSubscription": "创建商城订阅信息", "deleteMallSubscription": "删除商城订阅信息", "getMallSubscriptionList": "获取商城订阅信息列表", "updateMallSubscription": "更新商城订阅信息", "getMallUsageRecordById": "通过ID获取商城使用记录信息", "createMallUsageRecord": "创建商城使用记录信息", "deleteMallUsageRecord": "删除商城使用记录信息", "getMallUsageRecordList": "获取商城使用记录信息列表", "updateMallUsageRecord": "更新商城使用记录信息", "getMallRefundById": "通过ID获取商城退款信息", "createMallRefund": "创建商城退款信息", "deleteMallRefund": "删除商城退款信息", "getMallRefundList": "获取商城退款信息列表", "updateMallRefund": "更新商城退款信息", "getMallDeliveryById": "通过ID获取商城配送信息", "createMallDelivery": "创建商城配送信息", "deleteMallDelivery": "删除商城配送信息", "getMallDeliveryList": "获取商城配送信息列表", "updateMallDelivery": "更新商城配送信息", "getMallPaymentById": "通过ID获取商城支付信息", "createMallPayment": "创建商城支付信息", "deleteMallPayment": "删除商城支付信息", "getMallPaymentList": "获取商城支付信息列表", "updateMallPayment": "更新商城支付信息", "getMallPaymentLogById": "通过ID获取商城支付日志信息", "createMallPaymentLog": "创建商城支付日志信息", "deleteMallPaymentLog": "删除商城支付日志信息", "getMallPaymentLogList": "获取商城支付日志信息列表", "updateMallPaymentLog": "更新商城支付日志信息", "getMallRefundExpressById": "通过ID获取商城退款物流信息", "createMallRefundExpress": "创建商城退款物流信息", "deleteMallRefundExpress": "删除商城退款物流信息", "getMallRefundExpressList": "获取商城退款物流信息列表", "updateMallRefundExpress": "更新商城退款物流信息", "getMallProductCategoryTree": "获取商城商品分类树", "getMallLicenseById": "通过ID获取许可证信息", "batchCreateMallLicense": "批量添加许可证", "getMallLicenseList": "获取许可证信息列表", "getMallLicenseDeviceById": "通过ID获取许可证设备信息", "getMallLicenseDeviceList": "获取许可证设备信息列表", "getMallSupplierById": "通过ID获取供应商信息", "createMallSupplier": "创建供应商信息", "deleteMallSupplier": "删除供应商信息", "getMallSupplierList": "获取供应商信息列表", "updateMallSupplier": "更新供应商信息", "getMallSupplierCommissionById": "通过ID获取供应商分成信息", "getMallSupplierCommissionList": "获取供应商分成信息列表", "batchCreateMallProductSku": "批量创建商品SKU信息", "batchProcessMallProductSku": "批量处理商品SKU", "getMallApplicationById": "通过ID获取应用信息", "createMallApplication": "创建应用信息", "deleteMallApplication": "删除应用信息", "getMallApplicationList": "获取应用信息列表", "updateMallApplication": "更新应用信息", "getBoloLexiconById": "通过ID获取BoloLexicon信息", "createBoloLexicon": "创建BoloLexicon信息", "deleteBoloLexicon": "删除BoloLexicon信息", "getBoloLexiconList": "获取BoloLexicon信息列表", "updateBoloLexicon": "更新BoloLexicon信息", "importBoloLexicon": "批量导入违禁词", "unbindMallLicenseDevice": "解绑许可证设备", "healthCheck": "Health check", "universalReport": "Universal event report // 支持所有类型的事件上报：用户行为、业务事件、系统事件、用户信息、设备信息等", "getAnalyticsDashboard": "仪表盘", "getUniversalStats": "通用统计分析接口，支持多种业务场景的数据查询", "getAnalyticsApplicationById": "通过ID获取分析应用", "createAnalyticsApplication": "创建分析应用", "deleteAnalyticsApplication": "删除分析应用", "getAnalyticsApplicationList": "获取分析应用列表", "updateAnalyticsApplication": "更新分析应用", "getAnalyticsCohortById": "通过ID获取分析群组", "createAnalyticsCohort": "创建分析群组", "deleteAnalyticsCohort": "删除分析群组", "getAnalyticsCohortList": "获取分析群组列表", "updateAnalyticsCohort": "更新分析群组", "getAnalyticsDailySummaryById": "通过ID获取分析日报", "createAnalyticsDailySummary": "创建分析日报", "deleteAnalyticsDailySummary": "删除分析日报", "getAnalyticsDailySummaryList": "获取分析日报列表", "updateAnalyticsDailySummary": "更新分析日报", "getAnalyticsDeviceById": "通过ID获取分析设备", "createAnalyticsDevice": "创建分析设备", "deleteAnalyticsDevice": "删除分析设备", "getAnalyticsDeviceList": "获取分析设备列表", "updateAnalyticsDevice": "更新分析设备", "getAnalyticsEventById": "通过ID获取分析事件", "createAnalyticsEvent": "创建分析事件", "deleteAnalyticsEvent": "删除分析事件", "getAnalyticsEventList": "获取分析事件列表", "updateAnalyticsEvent": "更新分析事件", "getAnalyticsEventDefinitionById": "通过ID获取事件定义", "createAnalyticsEventDefinition": "创建事件定义", "deleteAnalyticsEventDefinition": "删除事件定义", "getAnalyticsEventDefinitionList": "获取事件定义列表", "updateAnalyticsEventDefinition": "更新事件定义", "getAnalyticsRealTimeMetricById": "通过ID获取实时指标", "createAnalyticsRealTimeMetric": "创建实时指标", "deleteAnalyticsRealTimeMetric": "删除实时指标", "getAnalyticsRealTimeMetricList": "获取实时指标列表", "updateAnalyticsRealTimeMetric": "更新实时指标", "getAnalyticsUserById": "通过ID获取分析用户", "createAnalyticsUser": "创建分析用户", "deleteAnalyticsUser": "删除分析用户", "getAnalyticsUserList": "获取分析用户列表", "updateAnalyticsUser": "更新分析用户", "getFunnelById": "获取漏斗详情", "runFunnelAnalysis": "运行漏斗分析", "getFunnelConditionById": "获取漏斗条件详情", "createFunnelCondition": "创建漏斗条件", "deleteFunnelCondition": "删除漏斗条件", "getFunnelConditionList": "获取漏斗条件列表", "updateFunnelCondition": "更新漏斗条件", "createFunnel": "创建漏斗", "deleteFunnel": "删除漏斗", "getFunnelList": "获取漏斗列表", "getFunnelStepById": "获取漏斗步骤详情", "createFunnelStep": "创建漏斗步骤", "deleteFunnelStep": "删除漏斗步骤", "getFunnelStepList": "获取漏斗步骤列表", "updateFunnelStep": "更新漏斗步骤", "updateFunnel": "更新漏斗", "getEventTrends": "事件趋势数据", "exportDashboardData": "导出仪表盘数据", "getGeographicData": "地理分布数据", "getRealtimeData": "实时数据", "getUserBehaviorHeatmap": "用户行为热力图数据", "getUserTrends": "用户趋势数据", "getPathAnalysis": "路径分析", "getEventConversions": "事件转换数据", "exportPathAnalysis": "导出路径分析数据", "getPathHeatmap": "路径热力图数据", "getPathStats": "路径统计趋势数据", "generateApiSecret": "生成API密钥", "resetApiSecret": "重置API密钥", "getRentalDeviceById": "通过ID获取租赁设备信息", "createRentalDevice": "创建租赁设备信息", "deleteRentalDevice": "删除租赁设备信息", "getRentalDeviceList": "获取租赁设备信息列表", "updateRentalDevice": "更新租赁设备信息", "getRentalDeviceMessageById": "通过ID获取租赁设备消息信息", "createRentalDeviceMessage": "创建租赁设备消息信息", "deleteRentalDeviceMessage": "删除租赁设备消息信息", "getRentalDeviceMessageList": "获取租赁设备消息信息列表", "updateRentalDeviceMessage": "更新租赁设备消息信息"}, "route": {"dashboard": "控制台", "systemManagementTitle": "系统管理", "menuManagementTitle": "菜单管理", "roleManagementTitle": "角色管理", "apiManagementTitle": "接口管理", "userManagementTitle": "用户管理", "fileManagement": "文件管理", "fileTagManagement": "文件标签管理", "userProfileTitle": "用户个人信息", "dictionaryManagementTitle": "字典管理", "dictionaryDetailManagementTitle": "键值管理", "oauthManagement": "第三方登录管理", "tokenManagement": "令牌管理", "otherPages": "其他页面", "departmentManagement": "部门管理", "positionManagement": "职位管理", "memberManagement": "会员管理", "memberRankManagement": "会员等级管理", "taskManagement": "定时任务管理", "emailProviderManagement": "邮箱服务配置管理", "smsProviderManagement": "短信服务配置管理", "messageCenterManagement": "消息中心管理", "storageProviderManagement": "云存储服务商管理", "cloudFileManagement": "云文件管理", "cloudFileTagManagement": "云文件标签管理", "configurationManagement": "参数管理", "adManagement": "广告管理", "communityBanner": "社区模块", "serviceBanner": "服务模块", "serviceInfoBanner": "服务咨询", "segmentManagement": "栏目管理", "communityManage": "社区管理", "reply": "回复管理", "groupManage": "圈子管理", "tagManage": "话题管理", "articleManagement": "文章管理", "articleEdite": "文章编辑", "segmentArticleRecord": "栏目文章记录管理", "editeSegment": "编辑栏目", "commentManagement": "评论管理", "address": "地址管理", "province": "省份管理", "city": "城市管理", "area": "区管理", "street": "街道管理", "userAddress": "用户地址管理", "appVersion": "App版本管理", "userManagement": "用户管理", "mtms": "售后管理", "productManage": "产品管理", "maintenInfo": "维修单管理", "relatedCommentArticles": "相关评论文章", "articleDetail": "文章详情", "articleEdit": "编辑文章", "maintenInfoDetail": "维修单详情", "officialWebsiteManagement": "官网管理", "devHistoryManagement": "发展历程管理", "cmsTagManage": "标签管理", "usercenterAppApiManagement": "APP接口管理", "usercenterAppRoleManagement": "APP角色管理", "meetMallManagement": "商城管理", "meetMallOrder": "订单管理", "meetMallOrderLog": "订单日志管理", "meetMallProduct": "商品管理", "meetMallProductCategory": "分类管理", "meetMallProductQuota": "配额管理", "meetMallProductSku": "SKU管理", "meetMallSubscription": "订阅管理", "meetMallUsageRecord": "使用记录管理", "meetMallOrderItem": "订单项管理", "meetMallRefund": "退款管理", "meetMallSupplier": "供应商管理", "meetMallSupplierCommission": "供应商分成管理", "meetMallLicense": "许可证管理", "meetMallLicenseDevice": "许可证设备管理", "meetMallApplication": "应用管理", "boluomiBanner": "播咯秘轮播图管理", "managementCenter": "管理中心", "analytics": "数据分析", "analyticsApplication": "分析应用", "analyticsCohort": "分析群组", "analyticsDailySummary": "分析日报", "analyticsDevice": "分析设备", "analyticsEvent": "分析事件", "analyticsEventDefinition": "事件定义", "analyticsRealTimeMetric": "实时指标", "analyticsUser": "分析用户", "analyticsDashboard": "数据分析仪表盘", "analyticsFunnel": "漏斗管理", "rentalDevice": "租赁设备管理", "rentalDeviceMessage": "租赁设备消息管理"}, "login": {"loginSuccessTitle": "登录成功", "signupSuccessTitle": "注册成功", "signupUserExist": "用户名或者邮箱已被注册", "userNotExist": "用户不存在", "wrongCaptcha": "验证码错误", "wrongUsernameOrPassword": "用户名或密码错误", "wrongPassword": "密码错误", "requireLogin": "请重新登录", "loginTypeForbidden": "此登录方式被管理员关闭，请尝试其他登录方式", "registerTypeForbidden": "此注册方式被管理员关闭，请尝试其他注册方式", "resetTypeForbidden": "此重置密码方式被管理员关闭，请尝试其他重置密码方式", "userBanned": "您的账户已停用或在审核中，请联系管理员", "mobileExist": "手机号已被注册"}, "menu": {"deleteChildrenDesc": "请先删除子菜单", "menuNotExists": "菜单不存在", "menuAlreadyExists": "菜单已存在", "parentNotExist": "父级不存在"}, "role": {"admin": "管理员", "stuff": "员工", "seller": "销售", "member": "会员", "changeStatusSuccess": "已成功修改角色状态", "changeStatusFailed": "修改角色状态失败", "duplicateRoleValue": "角色值重复", "userExists": "请先删除该角色下的用户", "roleForbidden": "您的角色已停用"}, "user": {"wrongPassword": "密码错误"}, "init": {"initializeIsRunning": "正在初始化...", "alreadyInit": "数据库已被初始化。"}, "dictionary": {"createDetailFailed": "创建字典键值失败, key已被使用"}, "oauth": {"createAccount": "请创建一个该邮箱的账号或绑定该邮箱到一个账号"}, "casbin": {"removeFailed": "无法删除旧规则", "addFailed": "无法添加新规则"}, "department": {"managementDepartment": "核心管理部门", "deleteDepartmentChildrenFirst": "部门存在子部门，请先删除子部门", "deleteDepartmentUserFirst": "部门下存在用户，请先删除部门所有用户"}, "position": {"userExistError": "该职位下存在用户，禁止删除", "ceo": "CEO"}, "mcms": {"email": {"subject": "【Shixi Admin】 的验证码", "content": "您的验证码为： "}}, "captcha": {"mcmsNotEnabled": "MCMS 服务未启动，请确认 MCMS 服务已将 Enabled 配置为 true"}, "cms": {"article": "文章"}}
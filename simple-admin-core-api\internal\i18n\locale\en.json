{"common": {"success": "Successfully", "failed": "Failed", "updateSuccess": "Update successfully", "updateFailed": "Update failed", "createSuccess": "Create successfully", "createFailed": "Create failed", "deleteSuccess": "Delete successfully", "deleteFailed": "Delete failed", "changeStatusSuccess": "Change status successfully", "changeStatusFailed": "Change status failed", "targetNotExist": "Target does not exist", "databaseError": "Database error", "redisError": "Redis error", "permissionDeny": "User does not have permission to access this interface", "constraintError": "Operation failed: Data conflict", "validationError": "Operation failed: Validation failed", "notSingularError": "Operation failed: Data not unique", "serviceUnavailable": "Service is unavailable, please check if the service is enabled", "cacheError": "Cache error"}, "apiGroup": {"cloud_file_tag": "Cloud File's Tag", "cloud_file": "Cloud File", "storage_provider": "Storage Provider", "member_rank": "Member Rank", "member": "Member", "file_tag": "File's Tag", "file": "File", "task_log": "Task Log", "task": "Task", "position": "Position", "department": "Department", "token": "Token", "oauth": "Third-party Log In", "dictionary": "Dictionary", "api": "API", "authority": "Authority", "captcha": "<PERSON><PERSON>", "menu": "<PERSON><PERSON>", "role": "Role", "user": "User", "email_provider": "Email Provider", "email_log": "<PERSON><PERSON>", "sms_provider": "SMS Provider", "sms_log": "SMS Log", "message_sender": "Send Message", "configuration": "System Configuration", "banner": "Banner Management", "segment_article_record": "Segment Article Record", "segment": "Segment Management", "article": "Article Management", "cms": "CMS Management", "websitecms": "WebSiteCMS Management", "province": "Province Management", "city": "City Management", "area": "Area Management", "street": "Street Management", "maintenInfo": "Maintenance Information Management", "product": "Product Management", "userAddress": "User Address Management", "reply": "Reply Management", "segmentArticleRecord": "Segment Article Record Management", "appVersion": "App Version Management", "devHistory": "Development History Management", "cmsTagManage": "Tag Management", "appApi": "MeetBoke App Api Management", "appRole": "MeetBoke App Role Management", "group": "Group Management", "groupFollow": "Group Follow Management", "groupMember": "Group Member Management", "maintenOrder": "Maintenance Order Management", "sync": "Sync Management", "tag": "Tag Management", "tagRecord": "Tag Record Management", "userAddres": "User Address Management", "appapi": "App Api Management", "approle": "App Role Management", "mallOrder": "Order Management", "mallOrderItem": "Order Item Management", "mallOrderLog": "Order Log Management", "mallProduct": "Product Management", "mallProductCategory": "Category Management", "mallProductQuota": "Quota Management", "mallProductSku": "SKU Management", "mallSubscription": "Subscription Management", "mallUsageRecord": "Usage Record Management", "mallRefund": "Refund Management", "mallDelivery": "Delivery Management", "mallPayment": "Payment Management", "mallPaymentLog": "Payment Log Management", "mallRefundExpress": "Refund Express Management", "mallLicense": "Mall License Management", "mallLicenseDevice": "Mall License Device Management", "mallSupplier": "Mall Supplier Management", "mallSupplierCommission": "Mall Supplier Commission Management", "mallApplication": "Mall Application Management", "boloLexicon": "Bolo Lexicon Management", "boluomiBanner": "BoLuoMi Banner Management", "app": "Application", "analytics": "Analytics", "analyticsApplication": "Analytics Application", "analyticsCohort": "Analytics Cohort", "analyticsDailySummary": "Analytics Daily Summary", "analyticsDevice": "Analytics Device", "analyticsEvent": "Analytics Event", "analyticsEventDefinition": "Analytics Event Definition", "analyticsRealTimeMetric": "Analytics Real Time Metric", "analyticsUser": "Analytics User", "rentalDevice": "rentalDevice", "rentalDeviceMessage": "rentalDeviceMessage"}, "apiDesc": {"createConfiguration": "Create a configuration", "updateConfiguration": "Update the configuration", "deleteConfiguration": "Delete configurations", "getConfigurationList": "Get configuration list", "getConfigurationById": "Get configuration by ID", "createCloudFileTag": "Create a cloud file tag", "updateCloudFileTag": "Update the cloud file tag", "deleteCloudFileTag": "Delete cloud file tags", "getCloudFileTagList": "Get cloud file tag list", "getCloudFileTagById": "Get cloud file tag by ID", "createFileTag": "Create a file tag", "updateFileTag": "Update the file tag", "deleteFileTag": "Delete file tags", "getFileTagList": "Get file tag list", "getFileTagById": "Get file tag by ID", "createStorageProvider": "Create a storage provider", "updateStorageProvider": "Update the storage provider", "deleteStorageProvider": "Delete storage providers", "getStorageProviderList": "Get storage provider list", "getStorageProviderById": "Get storage provider by ID", "uploadFileToCloud": "Upload file to cloud storage", "createCloudFile": "Create a cloud file", "updateCloudFile": "Update the cloud file", "deleteCloudFile": "Delete cloud files", "getCloudFileList": "Get cloud file list", "getCloudFileById": "Get cloud file by ID", "sendEmail": "Send Email", "sendSms": "Send SMS", "createEmailLog": "Create a email log", "updateEmailLog": "Update the email log", "deleteEmailLog": "Delete email logs", "getEmailLogList": "Get email log list", "getEmailLogById": "Get email log by ID", "createSmsLog": "Create a sms log", "updateSmsLog": "Update the sms log", "deleteSmsLog": "Delete sms logs", "getSmsLogList": "Get sms log list", "getSmsLogById": "Get sms log by ID", "createSmsProvider": "Create a sms provider", "updateSmsProvider": "Update the sms provider", "deleteSmsProvider": "Delete sms providers", "getSmsProviderList": "Get sms provider list", "getSmsProviderById": "Get sms provider by ID", "createEmailProvider": "Create a email provider", "updateEmailProvider": "Update the email provider", "deleteEmailProvider": "Delete email providers", "getEmailProviderList": "Get email provider list", "getEmailProviderById": "Get email provider by ID", "userLogin": "Log in (Required)", "userRegister": "Sign up (Required)", "userChangePassword": "Change user's password", "userInfo": "Get user's information (Required)", "userList": "Get user list", "userModify": "Modify user information", "userPermissions": "Get user permission code (Required)", "userProfile": "Get user profile (Required)", "updateProfile": "Modify user's profile (Required)", "createUser": "Create a user", "updateUser": "Update the user", "deleteUser": "Delete users", "logout": "Log out (Required)", "updateUserStatus": "Update user's status", "refreshToken": "Get a refresh token", "accessToken": "Get an access token", "createRole": "Create role information", "updateRole": "Update role information", "deleteRole": "Delete role information", "roleList": "Get role list", "setRoleStatus": "Set role status information", "createMenu": "Create menu information", "updateMenu": "Update menu information", "menuInfo": "Get the menu information", "menuList": "Get menu list", "menuRoleList": "Get role's menu list (Required)", "deleteMenu": "Delete menu information", "roleMenu": "Get role's <PERSON><PERSON> List (Required)", "createMenuParam": "Create menu parameters", "updateMenuParam": "Update menu parameters", "deleteMenuParam": "Delete menu extra parameters", "menuParamListByMenuId": "Get menu extra parameters by menu ID", "createApi": "Create API information", "updateApi": "Update API information", "deleteAPI": "Delete APIs", "APIInfo": "Get API information", "APIList": "Get API list", "authorityApiList": "Get API list for authority", "createOrUpdateApiAuthority": "Create or update API authorization information", "APIAuthorityOfRole": "Get API authorization information of role", "createOrUpdateMenuAuthority": "Create or update menu authorization information", "menuAuthorityOfRole": "Get menu authorization information of role", "captcha": "Get captcha information (Required)", "uploadFile": "Upload", "fileList": "File list", "deleteFile": "Delete a file", "updateFileInfo": "Update the file information", "setPublicStatus": "Change the file public status", "downloadFile": "Download file", "createDictionary": "Create dictionary information", "updateDictionary": "Update dictionary information", "deleteDictionary": "Delete dictionary information", "getDictionaryList": "Get dictionary list", "createDictionaryDetail": "Create a dictionary KV information", "updateDictionaryDetail": "Update a dictionary KV information", "deleteDictionaryDetail": "Delete dictionary KV information", "getDictionaryListDetail": "Get dictionary detail list by dictionary name", "getDictionaryById": "Get dictionary information by ID", "getDictionaryDetailById": "Get dictionary detail information by ID", "getDictionaryDetailByDictionaryName": "Get dictionary details by dictionary name", "createProvider": "Create a provider information", "updateProvider": "Update the provider information", "deleteProvider": "Delete provider information", "getProviderList": "Get provider list", "oauthLogin": "Third-party log in", "createToken": "Create a token", "updateToken": "Update the token", "deleteToken": "Delete tokens", "getTokenList": "Get token list", "setTokenStatus": "Set token's status", "forceLoggingOut": "Force logging out", "createDepartment": "Create a department", "updateDepartment": "Update the department", "deleteDepartment": "Delete departments", "getDepartmentList": "Get department list", "updateDepartmentStatus": "Modify the department status", "createPosition": "Create a position", "updatePosition": "Update the position", "deletePosition": "Delete positions", "getPositionList": "Get position list", "updatePositionStatus": "Modify the position status", "createMember": "Create a member", "updateMember": "Update the member", "deleteMember": "Delete members", "getMemberList": "Get member list", "updateMemberStatus": "Modify the member status", "createMemberRank": "Create a member rank", "updateMemberRank": "Update a member rank", "deleteMemberRank": "Delete member ranks", "getMemberRankList": "Get member rank list", "getUserById": "Get user information by ID", "getRoleById": "Get role information by ID", "getMenuById": "Get menu information by ID", "getApiById": "Get API information by ID", "getMenuParamById": "Get menu parameter information by ID", "getMemberById": "Get member information by ID", "getMemberRankById": "Get member rank information by ID", "getDepartmentById": "Get department information by ID", "getPositionById": "Get position information by ID", "getProviderById": "Get 3rd party provider information by ID", "getTokenById": "Get token information by ID", "createTask": "Create a task", "updateTask": "Update the task", "deleteTask": "Delete tasks", "getTaskList": "Get task list", "getTaskById": "Get task by ID", "createTaskLog": "Create a task log", "updateTaskLog": "Update the task log", "deleteTaskLog": "Delete task logs", "getTaskLogList": "Get task log list", "getTaskLogById": "Get task log by ID", "createArticle": "Create a article", "updateArticle": "Update the article", "deleteArticle": "Delete articles", "getArticleList": "Get article list", "getArticleById": "Get article by ID", "createSegment": "Create a segment", "updateSegment": "Update the segment", "deleteSegment": "Delete segments", "getSegmentList": "Get segment list", "getSegmentById": "Get segment by ID", "createBanner": "Create a segment", "updateBanner": "Update the segment", "deleteBanner": "Delete segments", "getBannerList": "Get segment list", "getBannerById": "Get segment by ID", "createUserAdres": "Create a user address", "updateUserAdres": "Update the user address", "deleteUserAdres": "Delete user address", "getUserAdresList": "Get user address list", "getUserAdresById": "Get user address by ID", "createReply": "Create a reply", "updateReply": "Update the reply", "deleteReply": "Delete replys", "getReplyList": "Get reply list", "getReplyById": "Get reply by ID", "createAppVersion": "Create a app version", "updateAppVersion": "Update the app version", "deleteAppVersion": "Delete app versions", "getAppVersionList": "Get app version list", "getAppVersionById": "Get app version by ID", "createGroup": "Create a group info", "updateGroup": "Update the group info", "deleteGroup": "Delete group infos", "getGroupList": "Get group info list", "getGroupById": "Get group info by ID", "createTag": "Create a tag info", "updateTag": "Update the tag info", "deleteTag": "Delete tag infos", "getTagList": "Get tag info list", "getTagById": "Get tag info by ID", "getTagByGroupId": "Get Tag By GroupId", "createProduct": "Create a product", "updateProduct": "Update the product", "deleteProduct": "Delete products", "getProductList": "Get product list", "getProductById": "Get product by ID", "createMaintenInfo": "Create a mainten info", "updateMaintenInfo": "Update the mainten info", "deleteMaintenInfo": "Delete mainten infos", "getMaintenInfoList": "Get mainten info list", "getMaintenInfoById": "Get mainten info by ID", "createProvince": "Create a province", "updateProvince": "Update the province", "deleteProvince": "Delete provinces", "getProvinceList": "Get province list", "getProvinceById": "Get province by ID", "createCity": "Create a city", "updateCity": "Update the city", "deleteCity": "Delete citys", "getCityList": "Get city list", "getCityById": "Get city by ID", "createArea": "Create a area", "updateArea": "Update the area", "deleteArea": "Delete areas", "getAreaList": "Get area list", "getAreaById": "Get area by ID", "createStreet": "Create a street", "updateStreet": "Update the street", "deleteStreet": "Delete streets", "getStreetList": "Get street list", "getStreetById": "Get street by ID", "createAppversion": "Create a appversion", "updateAppversion": "Update the appversion", "deleteAppversion": "Delete appversions", "getAppversionList": "Get appversion list", "getAppversionById": "Get appversion by ID", "createSegmentArticleRecord": "Create a segment article record", "updateSegmentArticleRecord": "Update the segment article record", "deleteSegmentArticleRecord": "Delete segment article records", "getSegmentArticleRecordList": "Get segment article record list", "getSegmentArticleRecordById": "Get segment article record by ID", "getArticleListWithoutDraft": "Get Article List Without Draft", "createHistory": "Create a history", "updateHistory": "Update the history", "deleteHistory": "Delete historys", "getHistoryList": "Get history list", "getHistoryById": "Get history by ID", "createAppApi": "Create a app api", "updateAppApi": "Update the app api", "deleteAppApi": "Delete app apis", "getAppApiList": "Get app api list", "getAppApiById": "Get app api by ID", "createAppRole": "Create a app role", "updateAppRole": "Update the app role", "deleteAppRole": "Delete app roles", "getAppRoleList": "Get app role list", "getAppRoleById": "Get app role by ID", "getAppapiById": "Get appapi by ID", "createAppapi": "Create appapi information", "deleteAppapi": "Delete appapi information", "getAppapiList": "Get appapi list", "updateAppapi": "Update appapi information", "getApproleById": "Get approle by ID", "createApprole": "Create approle information", "deleteApprole": "Delete approle information", "getApproleList": "Get approle list", "updateApprole": "Update approle information", "auditArticle": "Audit Article", "cancelTopArticle": "Cancel Top Article", "topArticle": "Top Article", "getGroupFollowById": "Get group follow by ID", "createGroupFollow": "Create group follow information", "deleteGroupFollow": "Delete group follow information", "getGroupFollowList": "Get group follow list", "updateGroupFollow": "Update group follow information", "getGroupMemberById": "Get group member by ID", "createGroupMember": "Create group member information", "deleteGroupMember": "Delete group member information", "getGroupMemberList": "Get group member list", "updateGroupMember": "Update group member information", "getMaintenOrderById": "Get mainten order by ID", "createMaintenOrder": "Create mainten order information", "deleteMaintenOrder": "Delete mainten order information", "getMaintenOrderList": "Get mainten order list", "updateMaintenOrder": "Update mainten order information", "syncArticleCommentNum": "Sync Article Comment Num", "syncArticleLikeNum": "Sync Article Like <PERSON>um", "syncArticleViewNum": "Sync Article View Num", "syncFollowAndFansInfo": "Sync Follow And Fans Info", "syncFollowInfo": "Sync Follow And Fans Info", "syncGroupInfo": "Sync Group Info", "syncTagInfo": "Sync Tag Info", "syncUserInfo": "Sync User Info", "getTagListByGroupId": "Get tag By Group ID", "getTagRecordById": "Get tag record by ID", "createTagRecord": "Create tag record information", "deleteTagRecord": "Delete tag record information", "getTagRecordList": "Get tag record list", "updateTagRecord": "Update tag record information", "getUserList": "Get user list", "updateUserForbiddenTime": "Update user forbidden time", "updateUserMutedTime": "Update user muted time", "getUserAddresById": "Get user addres by ID", "createUserAddres": "Create user addres information", "deleteUserAddres": "Delete user addres information", "getUserAddresList": "Get user addres list", "updateUserAddres": "Update user addres information", "getApiAuthority": "Get role's API authorization list", "getMallOrderById": "Get mall order by ID", "createMallOrder": "Create mall order information", "deleteMallOrder": "Delete mall order information", "getMallOrderList": "Get mall order list", "updateMallOrder": "Update mall order information", "getMallOrderItemById": "Get mall order item by ID", "createMallOrderItem": "Create mall order item information", "deleteMallOrderItem": "Delete mall order item information", "getMallOrderItemList": "Get mall order item list", "updateMallOrderItem": "Update mall order item information", "getMallOrderLogById": "Get mall order log by ID", "createMallOrderLog": "Create mall order log information", "deleteMallOrderLog": "Delete mall order log information", "getMallOrderLogList": "Get mall order log list", "updateMallOrderLog": "Update mall order log information", "getMallProductById": "Get mall product by ID", "createMallProduct": "Create mall product information", "deleteMallProduct": "Delete mall product information", "getMallProductList": "Get mall product list", "updateMallProduct": "Update mall product information", "getMallProductCategoryById": "Get mall product category by ID", "createMallProductCategory": "Create mall product category information", "deleteMallProductCategory": "Delete mall product category information", "getMallProductCategoryList": "Get mall product category list", "updateMallProductCategory": "Update mall product category information", "getMallProductQuotaById": "Get mall product quota by ID", "createMallProductQuota": "Create mall product quota information", "deleteMallProductQuota": "Delete mall product quota information", "getMallProductQuotaList": "Get mall product quota list", "updateMallProductQuota": "Update mall product quota information", "getMallProductSkuById": "Get mall product sku by ID", "createMallProductSku": "Create mall product sku information", "deleteMallProductSku": "Delete mall product sku information", "getMallProductSkuList": "Get mall product sku list", "updateMallProductSku": "Update mall product sku information", "getMallSubscriptionById": "Get mall subscription by ID", "createMallSubscription": "Create mall subscription information", "deleteMallSubscription": "Delete mall subscription information", "getMallSubscriptionList": "Get mall subscription list", "updateMallSubscription": "Update mall subscription information", "getMallUsageRecordById": "Get mall usage record by ID", "createMallUsageRecord": "Create mall usage record information", "deleteMallUsageRecord": "Delete mall usage record information", "getMallUsageRecordList": "Get mall usage record list", "updateMallUsageRecord": "Update mall usage record information", "syncArticleData": "Sync Article Data", "getMallRefundById": "Get mall refund by ID", "createMallRefund": "Create mall refund information", "deleteMallRefund": "Delete mall refund information", "getMallRefundList": "Get mall refund list", "updateMallRefund": "Update mall refund information", "getMallDeliveryById": "Get mall delivery by ID", "createMallDelivery": "Create mall delivery information", "deleteMallDelivery": "Delete mall delivery information", "getMallDeliveryList": "Get mall delivery list", "updateMallDelivery": "Update mall delivery information", "getMallPaymentById": "Get mall payment by ID", "createMallPayment": "Create mall payment information", "deleteMallPayment": "Delete mall payment information", "getMallPaymentList": "Get mall payment list", "updateMallPayment": "Update mall payment information", "getMallPaymentLogById": "Get mall payment log by ID", "createMallPaymentLog": "Create mall payment log information", "deleteMallPaymentLog": "Delete mall payment log information", "getMallPaymentLogList": "Get mall payment log list", "updateMallPaymentLog": "Update mall payment log information", "getMallRefundExpressById": "Get mall refund express by ID", "createMallRefundExpress": "Create mall refund express information", "deleteMallRefundExpress": "Delete mall refund express information", "getMallRefundExpressList": "Get mall refund express list", "updateMallRefundExpress": "Update mall refund express information", "getMallProductCategoryTree": "Get mall product category tree", "getMallLicenseById": "Get mall license by ID", "batchCreateMallLicense": "Batch create mall license", "getMallLicenseList": "Get mall license list", "getMallLicenseDeviceById": "Get mall license device by ID", "getMallLicenseDeviceList": "Get mall license device list", "getMallSupplierById": "Get mall supplier by ID", "createMallSupplier": "Create mall supplier information", "deleteMallSupplier": "Delete mall supplier information", "getMallSupplierList": "Get mall supplier list", "updateMallSupplier": "Update mall supplier information", "getMallSupplierCommissionById": "Get mall supplier commission by ID", "getMallSupplierCommissionList": "Get mall supplier commission list", "batchCreateMallProductSku": "Batch create mall product sku information", "batchProcessMallProductSku": "Batch process mall product sku", "getMallApplicationById": "Get mall application by ID", "createMallApplication": "Create mall application information", "deleteMallApplication": "Delete mall application information", "getMallApplicationList": "Get mall application list", "updateMallApplication": "Update mall application information", "getBoloLexiconById": "Get bolo lexicon by ID", "createBoloLexicon": "Create bolo lexicon information", "deleteBoloLexicon": "Delete bolo lexicon information", "getBoloLexiconList": "Get bolo lexicon list", "updateBoloLexicon": "Update bolo lexicon information", "importBoloLexicon": "Batch import bolo lexicon", "unbindMallLicenseDevice": "Unbind mall license device", "healthCheck": "健康检查", "universalReport": "通用事件上报", "getAnalyticsDashboard": "仪表盘", "getUniversalStats": "通用统计分析接口，支持多种业务场景的数据查询", "getAnalyticsApplicationById": "Get analytics application by ID", "createAnalyticsApplication": "Create analytics application information", "deleteAnalyticsApplication": "Delete analytics application information", "getAnalyticsApplicationList": "Get analytics application list", "updateAnalyticsApplication": "Update analytics application information", "getAnalyticsCohortById": "Get analytics cohort by ID", "createAnalyticsCohort": "Create analytics cohort information", "deleteAnalyticsCohort": "Delete analytics cohort information", "getAnalyticsCohortList": "Get analytics cohort list", "updateAnalyticsCohort": "Update analytics cohort information", "getAnalyticsDailySummaryById": "Get analytics daily summary by ID", "createAnalyticsDailySummary": "Create analytics daily summary information", "deleteAnalyticsDailySummary": "Delete analytics daily summary information", "getAnalyticsDailySummaryList": "Get analytics daily summary list", "updateAnalyticsDailySummary": "Update analytics daily summary information", "getAnalyticsDeviceById": "Get analytics device by ID", "createAnalyticsDevice": "Create analytics device information", "deleteAnalyticsDevice": "Delete analytics device information", "getAnalyticsDeviceList": "Get analytics device list", "updateAnalyticsDevice": "Update analytics device information", "getAnalyticsEventById": "Get analytics event by ID", "createAnalyticsEvent": "Create analytics event information", "deleteAnalyticsEvent": "Delete analytics event information", "getAnalyticsEventList": "Get analytics event list", "updateAnalyticsEvent": "Update analytics event information", "getAnalyticsEventDefinitionById": "Get analytics event definition by ID", "createAnalyticsEventDefinition": "Create analytics event definition information", "deleteAnalyticsEventDefinition": "Delete analytics event definition information", "getAnalyticsEventDefinitionList": "Get analytics event definition list", "updateAnalyticsEventDefinition": "Update analytics event definition information", "getAnalyticsRealTimeMetricById": "Get analytics real time metric by ID", "createAnalyticsRealTimeMetric": "Create analytics real time metric information", "deleteAnalyticsRealTimeMetric": "Delete analytics real time metric information", "getAnalyticsRealTimeMetricList": "Get analytics real time metric list", "updateAnalyticsRealTimeMetric": "Update analytics real time metric information", "getAnalyticsUserById": "Get analytics user by ID", "createAnalyticsUser": "Create analytics user information", "deleteAnalyticsUser": "Delete analytics user information", "getAnalyticsUserList": "Get analytics user list", "updateAnalyticsUser": "Update analytics user information", "getFunnelById": "Get Funnel By Id", "runFunnelAnalysis": "Run Funnel Analysis", "getFunnelConditionById": "Get Funnel Condition By Id", "createFunnelCondition": "Create Funnel Condition", "deleteFunnelCondition": "Delete Funnel Condition", "getFunnelConditionList": "Get Funnel Conditions", "updateFunnelCondition": "Update Funnel Condition", "createFunnel": "Create Funnel", "deleteFunnel": "Delete Funnel", "getFunnelList": "Get Funnel List", "getFunnelStepById": "Get Funnel Step By Id", "createFunnelStep": "Create Funnel Step", "deleteFunnelStep": "Delete Funnel Step", "getFunnelStepList": "Get Funnel Steps", "updateFunnelStep": "Update Funnel Step", "updateFunnel": "Update Funnel", "getEventTrends": "Event Trends", "exportDashboardData": "Export Dashboard Data", "getGeographicData": "Geographic Data", "getRealtimeData": "Realtime Data", "getUserBehaviorHeatmap": "User Behavior Heatmap", "getUserTrends": "User Trends", "getPathAnalysis": "Path Analysis", "getEventConversions": "Event Conversions", "exportPathAnalysis": "Export Path Analysis", "getPathHeatmap": "Path Heatmap", "getPathStats": "Path Stats", "generateApiSecret": "Generate API secret", "resetApiSecret": "Reset API secret", "getRentalDeviceById": "Get rental device by ID", "createRentalDevice": "Create rental device information", "deleteRentalDevice": "Delete rental device information", "getRentalDeviceList": "Get rental device list", "updateRentalDevice": "Update rental device information", "getRentalDeviceMessageById": "Get rental device message by ID", "createRentalDeviceMessage": "Create rental device message information", "deleteRentalDeviceMessage": "Delete rental device message information", "getRentalDeviceMessageList": "Get rental device message list", "updateRentalDeviceMessage": "Update rental device message information"}, "route": {"dashboard": "Dashboard", "systemManagementTitle": "System", "menuManagementTitle": "<PERSON><PERSON>", "roleManagementTitle": "Role", "apiManagementTitle": "API", "userManagementTitle": "User", "fileManagement": "File", "fileTagManagement": "File Tag", "userProfileTitle": "Profile", "dictionaryManagementTitle": "Dictionary", "dictionaryDetailManagementTitle": "Key/Value Management", "oauthManagement": "<PERSON><PERSON><PERSON>", "tokenManagement": "Token", "otherPages": "Other pages", "departmentManagement": "Department", "positionManagement": "Position", "memberManagement": "Member", "memberRankManagement": "Member Rank", "taskManagement": "Scheduled Task", "emailProviderManagement": "Email Provider", "smsProviderManagement": "SMS Provider", "messageCenterManagement": "Message Center", "storageProviderManagement": "Storage Provider", "cloudFileManagement": "Cloud File", "cloudFileTagManagement": "Cloud File's Tag", "configurationManagement": "System Config", "adManagement": "Ad Management", "communityBanner": "Community Banner", "serviceBanner": "Service Banner", "serviceInfoBanner": "ServiceInfo Banner", "segmentManagement": "Segment Management", "communityManage": "Community Manage", "reply": "Reply Management", "groupManage": "Coterie Management", "tagManage": "Tag Management", "articleManagement": "Article Management", "articleEdite": "Article Editing", "segmentArticleRecord": "SegmentArticleRecord Management", "editeSegment": "Edite Segment", "commentManagement": "Comment Management", "address": "Address Management", "province": "Province Management", "city": "City Management", "area": "Area Management", "street": "Street Management", "userAddress": "User Address Management", "appVersion": "App Version Management", "userManagement": "User Management", "mtms": "After-sales Management", "productManage": "Product Management ", "maintenInfo": "Maintenance Information Management", "relatedCommentArticles": "Related Comment Articles", "articleDetail": "Article Information", "articleEdit": "Edit Article", "maintenInfoDetail": "Maintenance Information", "officialWebsiteManagement": "Official Website Management", "devHistoryManagement": "Development History Management", "cmsTagManage": "Tag Management", "usercenterAppApiManagement": "Usercenter App Api Management", "usercenterAppRoleManagement": "Usercenter App Role Management", "meetMallManagement": "Mall Management", "meetMallOrder": "Order Management", "meetMallOrderLog": "Order Log Management", "meetMallProduct": "Goods Management", "meetMallProductCategory": "Category Management", "meetMallProductQuota": "Quota Management", "meetMallProductSku": "SKU Management", "meetMallSubscription": "Subscription Management", "meetMallUsageRecord": "Usage Record Management", "meetMallOrderItem": "Order Item Management", "meetMallRefund": "Refund Management", "meetMallSupplier": "Supplier Management", "meetMallSupplierCommission": "Supplier Commission Management", "meetMallLicense": "License Management", "meetMallLicenseDevice": "License Device Management", "meetMallApplication": "Application Management", "boloLexicon": "Bolo Lexicon Management", "boluomiBanner": "BoLuoMi Banner Management", "managementCenter": "Admin Center", "analytics": "Analytics", "analyticsApplication": "Analytics Application", "analyticsCohort": "Analytics Cohort", "analyticsDailySummary": "Analytics Daily Summary", "analyticsDevice": "Analytics Device", "analyticsEvent": "Analytics Event", "analyticsEventDefinition": "Analytics Event Definition", "analyticsRealTimeMetric": "Analytics Real Time Metric", "analyticsUser": "Analytics User", "analyticsDashboard": "Analytics Dashboard", "analyticsFunnel": "Analytics Funnel"}, "login": {"loginSuccessTitle": "Login successful", "signupSuccessTitle": "Sign up successful", "signupUserExist": "Username or email address had been registered", "userNotExist": "User does not register", "wrongCaptcha": "Wrong captcha", "wrongPassword": "Wrong Password", "wrongUsernameOrPassword": "Wrong username or password", "requireLogin": "Please log in again", "loginTypeForbidden": "This login method has been disabled by the administrator. Please try another login method", "registerTypeForbidden": "This registration method has been disabled by the administrator. Please try another registration method", "resetTypeForbidden": "This password reset method has been disabled by the administrator. Please try another password reset method", "userBanned": "Your account has been deactivated or is under review, please contact the administrator", "mobileExist": "This phone number had been registered"}, "menu": {"deleteChildrenDesc": "Please delete menu's children first", "menuNotExists": "Menu does not exist", "menuAlreadyExists": "Menu already exists", "parentNotExist": "The parent does not exist"}, "role": {"admin": "Administrator", "stuff": "Stuff", "seller": "<PERSON><PERSON>", "member": "Member", "changeStatusSuccess": "Change role status successfully", "changeStatusFailed": "Change role status failed", "duplicateRoleValue": "Duplicate role value", "userExists": "Please delete users who belong to this role", "roleForbidden": "Your role is forbidden"}, "user": {"wrongPassword": "Wrong password"}, "init": {"alreadyInit": "The database had been initialized.", "initializeIsRunning": "The initialization is running..."}, "dictionary": {"createDetailFailed": "Create Key/Value failed, key had been used"}, "oauth": {"createAccount": "Please register an account with this email or bind the email to an account"}, "casbin": {"removeFailed": "Failed to remove old policies", "addFailed": "Failed to add new policies"}, "department": {"managementDepartment": "Management Department", "deleteDepartmentChildrenFirst": "The department has sub-departments, please delete the sub-departments first", "deleteDepartmentUserFirst": "There are users under the department, please delete all users in the department first"}, "position": {"userExistError": "There are users under this position, it is forbidden to delete", "ceo": "CEO"}, "mcms": {"email": {"subject": "Verification Code for 【Shixi Admin】", "content": "Your verification code is: "}}, "captcha": {"mcmsNotEnabled": "MCMS service is not running，make sure that MCMS service's \"Enabled\" configuration is set to \"true\""}, "cms": {"article": "Article"}}